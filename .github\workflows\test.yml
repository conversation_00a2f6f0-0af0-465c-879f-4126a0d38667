name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'
  JWT_SECRET: 'test-jwt-secret-key-for-testing-only'

jobs:
  # Fast feedback: Lint and unit tests first
  lint-and-unit:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run unit tests with coverage
      run: npm run test:unit -- --coverage --reporter=verbose
      
    - name: Upload unit test coverage
      uses: codecov/codecov-action@v3
      if: always()
      with:
        file: ./coverage/coverage-final.json
        flags: unit
        name: unit-tests

  # Integration tests with MongoDB Memory Server
  integration:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: lint-and-unit

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run integration tests
      run: npm run test:integration -- --reporter=verbose
      env:
        NODE_ENV: test
        JWT_SECRET: test-jwt-secret-key-for-testing-only
        
    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: test-results/
        if-no-files-found: ignore

  # Security and dependency scanning
  security:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run npm audit
      run: npm audit --audit-level moderate
      continue-on-error: true
        
    - name: Upload security scan results
      if: always()
      run: echo "Security scan completed"
