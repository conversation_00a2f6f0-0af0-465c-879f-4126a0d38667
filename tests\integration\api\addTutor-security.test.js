/**
 * @fileoverview Integration tests for addTutor API security
 * @description Tests CSRF protection and security headers for tutor management endpoints
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import request from 'supertest';
import jwt from 'jsonwebtoken';
import { createServer } from 'http';
import createVercelCompatibleResponse from '../../utils/createVercelCompatibleResponse.js';
import addTutorHandler from '../../../api/addTutor.js';

// Note: We focus on functional testing rather than mocking implementation details
// The security functions are tested by verifying their actual effects (headers, behavior)

describe('Add Tutor API Security Integration Tests', () => {
  let mongoServer;
  let app;
  let adminToken;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    // Handle existing connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }
    await mongoose.connect(mongoUri);
    console.log('Test database connected successfully');

    // Create HTTP server with addTutor handler
    app = createServer((req, res) => {
      // ✅ SECURITY FIX: Use shared response helper with proper charset handling
      createVercelCompatibleResponse(res);

      // Parse request body for POST/PUT/DELETE requests
      if (['POST', 'PUT', 'DELETE'].includes(req.method)) {
        let body = '';
        req.on('data', chunk => {
          body += chunk.toString();
        });
        req.on('end', () => {
          try {
            req.body = JSON.parse(body);
          } catch (e) {
            req.body = {};
          }
          addTutorHandler(req, res);
        });
      } else {
        addTutorHandler(req, res);
      }
    });

    // Generate admin JWT token for testing
    adminToken = jwt.sign(
      { email: '<EMAIL>', role: 'admin' },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    await mongoose.connection.close();
    await mongoServer.stop();
    console.log('Test database torn down successfully');
  });

  beforeEach(async () => {
    // Clear database before each test
    await mongoose.connection.db.dropDatabase();
  });

  describe('Security Headers', () => {
    it('should apply comprehensive security headers for all requests', async () => {
      const response = await request(app)
        .post('/')
        .set('Cookie', `token=${adminToken}`)
        .send({ name: 'Test Tutor', email: '<EMAIL>' });

      // Verify security headers are present in response (actual behavior)
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY');
    });

    it('should apply security headers even for method not allowed', async () => {
      const response = await request(app)
        .get('/')
        .expect(405);

      // Verify security headers are present even for rejected methods
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY');
    });
  });

  describe('CSRF Protection', () => {
    it('should process POST requests with proper authentication', async () => {
      const response = await request(app)
        .post('/')
        .set('Cookie', `token=${adminToken}`)
        .send({
          name: 'Test Tutor',
          email: '<EMAIL>',
          subjects: ['Math'],
          costRange: '£20-30'
        });

      // Should process the request (may fail on other validation, but CSRF is handled)
      expect([200, 201, 400]).toContain(response.status);
    });

    it('should process PUT requests with proper authentication', async () => {
      const response = await request(app)
        .put('/')
        .set('Cookie', `token=${adminToken}`)
        .send({ id: 'test-id', name: 'Updated Tutor' });

      // Should process the request (may fail on validation, but not on CSRF)
      // 500 is acceptable here due to business logic validation errors
      expect([200, 400, 404, 500]).toContain(response.status);
    });

    it('should process DELETE requests with proper authentication', async () => {
      const response = await request(app)
        .delete('/')
        .set('Cookie', `token=${adminToken}`)
        .send({ id: 'test-id' });

      // Should process the request (may fail on validation, but not on CSRF)
      expect([200, 400, 404, 500]).toContain(response.status);
    });

    it('should handle requests without authentication', async () => {
      const response = await request(app)
        .post('/')
        .send({ name: 'Test Tutor' });

      // Should fail due to missing authentication
      expect(response.status).toBe(401);
    });
  });

  describe('Method Validation', () => {
    it('should reject GET requests with 405', async () => {
      const response = await request(app)
        .get('/')
        .expect(405);

      expect(response.text).toContain('Method GET Not Allowed');

      // Verify security headers are present
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
    });

    it('should set proper Allow header for unsupported methods', async () => {
      const response = await request(app)
        .patch('/')
        .expect(405);

      // The Allow header is returned as a string, not an array
      expect(response.headers.allow).toBe('POST, PUT, DELETE');
    });
  });

  describe('Security Integration', () => {
    it('should apply security headers for all HTTP methods', async () => {
      // Test POST
      const postResponse = await request(app)
        .post('/')
        .set('Cookie', `token=${adminToken}`)
        .send({ name: 'Test Tutor' });

      expect(postResponse.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(postResponse.headers).toHaveProperty('x-frame-options', 'DENY');

      // Test PUT
      const putResponse = await request(app)
        .put('/')
        .set('Cookie', `token=${adminToken}`)
        .send({ id: 'test-id' });

      expect(putResponse.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(putResponse.headers).toHaveProperty('x-frame-options', 'DENY');

      // Test DELETE
      const deleteResponse = await request(app)
        .delete('/')
        .set('Cookie', `token=${adminToken}`)
        .send({ id: 'test-id' });

      expect(deleteResponse.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(deleteResponse.headers).toHaveProperty('x-frame-options', 'DENY');
    });

    it('should handle authentication failures gracefully', async () => {
      const response = await request(app)
        .post('/')
        .send({ name: 'Test Tutor' });

      // Should fail due to missing authentication
      expect(response.status).toBe(401);

      // Security headers should still be present
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY');
    });
  });
});
