# Node modules
node_modules/


# Environment variables
.env

# Vercel-specific build output
.vercel/

# OS-specific files
.DS_Store
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test coverage
coverage/

# Security reports
security-report.json

# Security documentation (contains sensitive information)
docs/security-requirements.md
security-requirements.md

# Vercel and environment files
.vercel
.env
.env.*
*.pem
*key.json
