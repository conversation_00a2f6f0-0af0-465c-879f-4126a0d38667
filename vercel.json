{"version": 2, "rewrites": [{"source": "/page/:slug", "destination": "/page.html"}, {"source": "/", "destination": "/index.html"}, {"source": "/parents", "destination": "/parents.html"}, {"source": "/tutor<PERSON><PERSON>", "destination": "/api/protected.js?role=tutor&html=true"}, {"source": "/admin", "destination": "/api/protected.js?role=admin&html=true"}, {"source": "/login", "destination": "/login.html"}, {"source": "/tutors/search", "destination": "/api/tutors.js"}, {"source": "/tutors", "destination": "/api/tutors.js"}, {"source": "/about", "destination": "/about-us.html"}, {"source": "/about-us", "destination": "/about-us.html"}, {"source": "/api/login", "destination": "/api/login.js"}, {"source": "/api/connection", "destination": "/api/connection.js"}, {"source": "/api/addTutor", "destination": "/api/addTutor.js"}, {"source": "/blog-writer", "destination": "/api/blog-writer.js"}, {"source": "/api/blog-writer", "destination": "/api/blog-writer.js"}, {"source": "/blog", "destination": "/api/content-display.js"}, {"source": "/blog/:id", "destination": "/api/content-display.js"}, {"source": "/api/upload-image", "destination": "/api/upload-image.js"}, {"source": "/api/sections", "destination": "/api/sections.js"}, {"source": "/api/video-sections", "destination": "/api/video-sections.js"}, {"source": "/api/content-manager", "destination": "/api/content-manager.js"}, {"source": "/api/page", "destination": "/api/content-display.js"}, {"source": "/protected", "destination": "/api/protected.js"}]}