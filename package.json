{"name": "tutor-scotland", "version": "0.0.0", "description": "tutorScotland", "main": "index.html", "scripts": {"start": "vercel dev", "test": "vitest", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e", "test:pre-commit": "npm run test:unit && npm run test:migration", "test:dynamic-sections:baseline": "node scripts/test-dynamic-sections.js baseline", "test:dynamic-sections:validate": "node scripts/test-dynamic-sections.js validate", "test:dynamic-sections:styling": "playwright test tests/e2e/dynamic-sections-styling.spec.js", "test:dynamic-sections:visual": "playwright test tests/e2e/dynamic-sections-visual-regression.spec.js", "test:dynamic-sections:cross-browser": "playwright test tests/e2e/dynamic-sections-cross-browser.spec.js", "test:migration": "vitest run tests/migration/ tests/integration/models/", "test:schema-safety": "vitest run tests/integration/models/section-schema.test.js", "db:fix-slug-index": "node scripts/fix-slug-index.js", "security:check": "node scripts/security-check.js", "security:audit": "npm audit --audit-level=moderate", "security:fix": "npm audit fix", "prepare": "husky install", "pre-push": "npm run test:all"}, "author": {"name": "<PERSON>"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@vercel/blob": "^0.27.3", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "formidable": "^3.5.4", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "openai": "^4.85.0", "sharp": "^0.33.3", "uuid": "^11.1.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@vitest/coverage-v8": "^2.1.9", "husky": "^8.0.3", "mongodb-memory-server": "^9.5.0", "msw": "^2.10.4", "supertest": "^6.3.4", "vitest": "^2.1.9"}, "engines": {"bun": ">=0.1.0"}, "license": "MIT"}