/* header-banner.css */
/* This file contains the header and rolling banner styles from styles2.css */
/*
 * CROSS-MODULE DEPENDENCIES:
 * - Establishes: 40px padding pattern used by nav.css
 * - Coordinates with: nav.css for vertical alignment
 * - Extended by: responsive modules for mobile adjustments
 *
 * LOAD ORDER: 6th - loads before nav.css to establish base patterns
 * PURPOSE: Header, rolling banner, and login elements
 */

/* Header styles */
/* Specificity: 0,0,0,1 - Base header styling */
header {
    /* Two layered backgrounds: shield on top, tartan behind */
    background-image: url('/images/bannerShield2.png'), url('/images/bannerBackground.PNG');
    background-repeat: no-repeat, repeat;
    /* Position the shield 20px from the left & top,
       while the tartan repeats from the top-left. */
    background-position: 20px 20px, left top;
    /* Scale the shield to 100px wide,
       the tartan tiles to 50px wide. */
    background-size: 100px auto, 150px auto;
    /* Banner padding (height) - PATTERN: Used by nav.css for alignment */
    padding: 40px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Move and resize the heading */
header h1 {
    /* Slightly bigger heading */
    font-size: 3.3em;
    font-weight: bold;
    /* Nudge it so it starts after the shield area */
    margin-left: 100px;
    line-height: 1.2;
    text-shadow: 5px 5px 10px #0057B7;
}

/* Lilac box for the login link in the banner */
.login-box {
    background-color: #0057B7; /* blue */
    color: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
}

.login-box:hover {
    background-color: #b37cb3; /* a darker lilac on hover */
}

/* This container holds both buttons side by side */
.header-links {
    display: flex;
    /* A small gap for the space between them (adjust as needed) */
    gap: 10px;
    /* Right-align the buttons */
    margin-left: auto;
}

/* Legacy navigation styles removed - now handled by /css/nav.css */

/* Custom pages dropdown styles */
.custom-pages-dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #001B44;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    top: 100%;
    left: 0;
}

.dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    border-bottom: 1px solid #003F8F;
}

.dropdown-content a:hover {
    background-color: #003F8F;
}

.custom-pages-dropdown:hover .dropdown-content {
    display: block;
}

/* Rolling banner styles */
.rolling-banner {
    position: relative;
    width: 100%;
    overflow: hidden;
    border: 3px solid #C8A2C8; /* Lilac outline */
    background-color: #f9f9f9; /* If you want a background */
    height: 2.5em; /* Enough height for the text */
    display: flex;
    align-items: center; /* vertically center the text */
    margin-bottom: 10px;
    padding: 0 40px; /* PATTERN: Matches header padding - used by nav.css */
    box-sizing: border-box; /* Include padding in width calculation */
}

.rolling-content {
    white-space: nowrap;
    display: inline-block;
    padding-left: 100%;
    animation: scrollBanner 15s linear infinite;
    font-weight: bold;
    color: #0057B7;
}

@keyframes scrollBanner {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-100%);
    }
}

/* Responsive adjustments */
@media (max-width: 900px) {
    /* Remove padding on mobile for full-width rolling banner */
    .rolling-banner {
        padding: 0;
    }
}
